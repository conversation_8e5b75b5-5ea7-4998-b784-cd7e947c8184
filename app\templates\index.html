<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>夸克自动转存</title>
  <!-- CSS -->
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.2/dist/css/bootstrap.min.css">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
  <!-- Vue.js & Libs -->
  <script src="https://cdn.jsdelivr.net/npm/jquery@3.5.1/dist/jquery.slim.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.2/dist/js/bootstrap.bundle.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/vue@2.7.14/dist/vue.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/axios@0.21.4/dist/axios.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/v-jsoneditor@1.4.1/dist/v-jsoneditor.min.js"></script>
  <style>
    /* --- 1. 配色方案: 深蓝与灰 --- */
    :root {
      --primary-color: #4f46e5;      /* 主色 (靛蓝) */
      --primary-hover: #4338ca;      /* 主色悬浮 */
      --background-color: #f8fafc;  /* 背景色 (最浅灰) */
      --surface-color: #ffffff;      /* 表面色 (白) */
      --border-color: #e2e8f0;       /* 边框色 (浅灰) */
      --text-primary: #1e293b;       /* 主要文字 (深灰) */
      --text-secondary: #64748b;     /* 次要文字 (中灰) */
      --highlight-bg: #eef2ff;       /* 高亮背景 (浅靛蓝) */
      --status-active: #22c55e;      /* 状态: 激活 (绿) */
      --status-disabled: #9ca3af;     /* 状态: 禁用 (灰) */
      --status-warning: #f59e0b;      /* 状态: 警告 (黄) */
    }

    /* --- 2. 布局与滚动修复 --- */
    html, body {
      height: 100%;
      overflow: hidden; /* 防止body滚动 */
    }
    #app {
      display: flex;
      flex-direction: column;
      height: 100%;
    }
    .navbar {
      flex-shrink: 0; /* 防止导航栏收缩 */
      z-index: 1030;
    }
    .container-fluid {
      flex-grow: 1;
      height: 100%;
      padding: 0;
    }
    .row {
      height: 100%;
      margin: 0;
    }
    #sidebarMenu {
      height: calc(100vh - 56px); /* 视口高度减去导航栏高度 */
      background-color: var(--surface-color);
      border-right: 1px solid var(--border-color);
      flex-shrink: 0;
    }
    main {
      height: calc(100vh - 56px); /* 关键样式：仅主内容区可滚动 */
      overflow-y: auto; 
      flex-grow: 1;
    }

    /* --- 3. 通用UI优化 --- */
    body { background-color: var(--background-color); color: var(--text-primary); }
    .card {
      border: 1px solid var(--border-color); border-radius: .75rem;
      box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    }
    .card-header { background-color: var(--surface-color); font-weight: 500; border-bottom: 1px solid var(--border-color); }
    .btn-primary { background-color: var(--primary-color); border-color: var(--primary-color); }
    .btn-primary:hover, .btn-primary:focus { background-color: var(--primary-hover); border-color: var(--primary-hover); }
    .nav-link { color: var(--text-secondary); }
    .nav-link.active {
      font-weight: 600; color: var(--primary-color) !important;
      background-color: var(--highlight-bg) !important; border-radius: .5rem;
    }
    .nav-link:hover { color: var(--primary-color); }
    .sidebar .nav-link i { width: 24px; }
    .nav-bottom {
      position: absolute; bottom: 15px; width: 100%; padding: 0 1rem;
      text-align: center; font-size: 0.8rem;
    }
    .nav-bottom p { margin-bottom: 0.5rem; }
    .nav-bottom a { color: var(--text-secondary); }
    .nav-bottom a:hover { color: var(--primary-color); }
    
    /* --- 4. 任务状态指示灯 --- */
    .task-status-indicator {
        width: 10px; height: 10px; border-radius: 50%;
        display: inline-block; vertical-align: middle; margin-right: 8px;
        transition: background-color 0.3s;
    }
    .status-active { background-color: var(--status-active); }
    .status-disabled { background-color: var(--status-disabled); }
    .status-warning { background-color: var(--status-warning); }

    /* --- 5. 日志与返回顶部按钮 --- */
    .log-panel {
      background-color: var(--surface-color);
      color: var(--text-primary);
      border: 1px solid var(--border-color);
      font-family: 'SF Mono', 'Fira Code', monospace;
      font-size: 0.875rem; border-radius: 0.5rem; padding: 1rem; height: 65vh; overflow-y: auto; white-space: pre-wrap; word-break: break-all;
    }
    .log-highlight { background-color: #fef9c3; color: #713a0a; }
    #backToTopBtn {
      position: fixed; bottom: 20px; right: 20px; z-index: 1030; width: 45px; height: 45px; border-radius: 50%;
      background-color: var(--surface-color); border: 1px solid var(--border-color);
      box-shadow: 0 4px 6px -1px rgba(0,0,0,.1); display: none; align-items: center; justify-content: center; font-size: 1.2rem;
    }
    main { padding-bottom: 3rem; }
  </style>
</head>

<body>
  <div id="app">
    <nav class="navbar navbar-dark sticky-top bg-dark flex-md-nowrap p-0 shadow-sm">
      <a class="navbar-brand col-md-3 col-lg-2 mr-0 px-3" href="#"><i class="bi bi-clouds-fill"></i> 夸克自动转存</a>
      <button class="navbar-toggler position-absolute d-md-none collapsed" type="button" data-toggle="collapse" data-target="#sidebarMenu"><span class="navbar-toggler-icon"></span></button>
      
      <div class="d-flex align-items-center ml-auto pr-3">
        <button class="btn btn-success btn-sm mr-2" @click.prevent="saveConfig" title="保存配置 (Ctrl+S)">
          <i class="bi bi-save-fill"></i> 保存
        </button>
        <button class="btn btn-primary btn-sm" @click.prevent="runScriptNow()" title="运行所有任务 (Ctrl+R)">
          <i class="bi bi-play-circle-fill"></i> 运行
        </button>
      </div>
    </nav>

    <div class="container-fluid">
      <div class="row">
        <nav id="sidebarMenu" class="col-md-3 col-lg-2 d-md-block sidebar collapse">
          <div class="sidebar-sticky pt-3 px-2">
            <ul class="nav flex-column">
              <li class="nav-item mb-1"><a class="nav-link" href="#" :class="{active: activeTab === 'tasklist'}" @click="changeTab('tasklist')"><i class="bi bi-list-task"></i> 任务列表 <span class="badge badge-pill ml-1" style="background-color: var(--primary-color); color: white;" v-if="formData.tasklist.length">{{ formData.tasklist.length }}</span></a></li>
              <li class="nav-item mb-1"><a class="nav-link" href="#" :class="{active: activeTab === 'config'}" @click="changeTab('config')"><i class="bi bi-gear-fill"></i> 系统配置</a></li>
              <li class="nav-item mb-1"><a class="nav-link" href="#" :class="{active: activeTab === 'logs'}" @click="changeTab('logs')"><i class="bi bi-body-text"></i> 日志</a></li>
            </ul>
            
            <div class="nav-bottom text-center">
                <p><a href="/logout"><i class="bi bi-box-arrow-right mr-1"></i>退出</a></p>
                <p><a target="_blank" href="https://github.com/Cp0204/quark-auto-save/wiki"><i class="bi bi-wechat mr-1"></i>使用交流</a></p>
                <p><a href="./static/js/qas.addtask.user.js"><i class="bi bi-cloud-plus-fill mr-1"></i>推送任务油猴脚本</a></p>
                <p><a target="_blank" href="https://github.com/Cp0204/quark-auto-save"><i class="bi bi-github mr-1"></i>quark-auto-save</a></p>
                <p style="position: relative;" v-html="versionTips"></p>
            </div>
          </div>
        </nav>

        <main role="main" class="col-md-9 ml-sm-auto col-lg-10 px-md-4 py-4">
          <form @submit.prevent="saveConfig" @keydown="handleKeyDown">
            
            <div v-if="activeTab === 'config'">
              <div class="d-flex justify-content-between align-items-center pb-2 mb-3 border-bottom"><h1 class="h2">系统配置</h1></div>
              <div class="row">
                <div class="col-lg-6">
                  <div class="card mb-4"><div class="card-header d-flex justify-content-between align-items-center"><h5 class="mb-0"><i class="bi bi-cookie mr-2"></i>Cookie</h5><button type="button" class="btn btn-outline-secondary btn-sm" @click="addCookie()"><i class="bi bi-plus-lg"></i> 添加</button></div><div class="card-body p-4"><p class="card-text text-muted small">1. 所有账号执行签到<br>2. 仅第一个账号执行转存。</p><div v-for="(value, index) in formData.cookie" :key="index" class="input-group mb-2"><div class="input-group-prepend"><span class="input-group-text">#{{ index + 1 }}</span></div><input type="text" v-model="formData.cookie[index]" class="form-control" placeholder="在此粘贴Cookie..."><div class="input-group-append"><button type="button" class="btn btn-outline-danger" @click="removeCookie(index)" title="删除"><i class="bi bi-trash"></i></button></div></div><div v-if="!formData.cookie.length" class="alert alert-light text-center small p-2">暂无Cookie</div></div></div>
                  <div class="card mb-4"><div class="card-header"><h5 class="mb-0"><i class="bi bi-clock-history mr-2"></i>定时规则</h5></div><div class="card-body p-4"><div class="input-group"><input type="text" v-model="formData.crontab" class="form-control" placeholder="例如: 0 */2 * * * (每2小时运行一次)"><div class="input-group-append"><a href="https://tool.lu/crontab/" target="_blank" class="btn btn-outline-secondary" title="CRON表达式在线生成与校验">?</a></div></div></div></div>
                  <div class="card mb-4"><div class="card-header d-flex justify-content-between align-items-center"><h5 class="mb-0"><i class="bi bi-slash-circle-fill mr-2"></i>文件黑名单</h5><button type="button" class="btn btn-outline-secondary btn-sm" @click="addBlacklistItem()"><i class="bi bi-plus-lg"></i> 添加</button></div><div class="card-body p-4"><p class="card-text text-muted small">匹配的文件名将被跳过，不支持正则。</p><div v-for="(item, index) in formData.file_blacklist" :key="index" class="input-group mb-2"><input type="text" v-model="formData.file_blacklist[index]" class="form-control" placeholder="输入要屏蔽的完整文件名"><div class="input-group-append"><button type="button" class="btn btn-outline-danger" @click="removeBlacklistItem(index)" title="删除"><i class="bi bi-trash"></i></button></div></div><div v-if="!formData.file_blacklist || !formData.file_blacklist.length" class="alert alert-light text-center small p-2">暂无黑名单规则</div></div></div>
                </div>
                <div class="col-lg-6">
                  <div class="card mb-4"><div class="card-header d-flex justify-content-between align-items-center"><h5 class="mb-0"><i class="bi bi-bell-fill mr-2"></i>通知推送</h5><div><button type="button" class="btn btn-outline-info btn-sm mr-2" title="通知推送测试" @click="testPush()"><i class="bi bi-lightning-fill"></i> 测试</button><button type="button" class="btn btn-outline-secondary btn-sm" @click="addPush()"><i class="bi bi-plus-lg"></i> 添加</button></div></div><div class="card-body p-4"><p class="card-text text-muted small">支持多个通知渠道。</p><div v-for="(value, key) in formData.push_config" :key="key" class="input-group mb-2"><div class="input-group-prepend"><span class="input-group-text" style="min-width: 120px;">{{ key }}</span></div><input type="text" v-model="formData.push_config[key]" class="form-control"><div class="input-group-append"><button type="button" class="btn btn-outline-danger" @click="removePush(key)"><i class="bi bi-trash"></i></button></div></div><div v-if="!Object.keys(formData.push_config).length" class="alert alert-light text-center small p-2">暂无通知配置</div></div></div>
                  <div class="card mb-4">
                    <div class="card-header"><h5 class="mb-0"><i class="bi bi-keyboard-fill mr-2"></i>快捷键设置</h5></div>
                    <div class="card-body p-4">
                      <div class="d-flex justify-content-between align-items-center mb-3">
                        <label for="saveShortcutSwitch" class="mb-0">保存配置 (Ctrl/Cmd + S)</label>
                        <div class="custom-control custom-switch"><input type="checkbox" class="custom-control-input" id="saveShortcutSwitch" v-model="formData.shortcuts.saveEnabled"><label class="custom-control-label" for="saveShortcutSwitch"></label></div>
                      </div>
                      <div class="d-flex justify-content-between align-items-center">
                        <label for="runShortcutSwitch" class="mb-0">运行任务 (Ctrl/Cmd + R)</label>
                        <div class="custom-control custom-switch"><input type="checkbox" class="custom-control-input" id="runShortcutSwitch" v-model="formData.shortcuts.runEnabled"><label class="custom-control-label" for="runShortcutSwitch"></label></div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="col-12"><div class="card mb-4"><div class="card-header d-flex justify-content-between align-items-center"><h5 class="mb-0"><i class="bi bi-magic mr-2"></i>魔法匹配</h5><button type="button" class="btn btn-outline-secondary btn-sm" @click="addMagicRegex()"><i class="bi bi-plus-lg"></i> 添加</button></div><div class="card-body p-4"><p class="card-text text-muted small">预定义正则匹配规则。</p><div v-for="(value, key) in formData.magic_regex" :key="key" class="form-group mb-2"><div class="input-group"><div class="input-group-prepend"><span class="input-group-text">魔法名</span></div><input type="text" :data-oldkey="key" :value="key" class="form-control" @change="updateMagicRegexKey($event.target.dataset.oldkey, $event.target.value)" placeholder="自定义名称"><div class="input-group-prepend"><span class="input-group-text">正则处理</span></div><input type="text" v-model="value.pattern" class="form-control" placeholder="匹配表达式"><input type="text" v-model="value.replace" class="form-control" placeholder="替换表达式"><div class="input-group-append"><button type="button" class="btn btn-outline-danger" @click="removeMagicRegex(key)"><i class="bi bi-trash"></i></button></div></div></div><div v-if="!Object.keys(formData.magic_regex).length" class="alert alert-light text-center small p-2">暂无魔法匹配规则</div></div></div></div>
              </div>
            </div>

            <div v-if="activeTab === 'logs'">
                <div class="d-flex justify-content-between align-items-center pb-2 mb-3 border-bottom"><h1 class="h2">运行日志</h1><div class="col-5"><div class="input-group"><div class="input-group-prepend"><span class="input-group-text"><i class="bi bi-search"></i></span></div><input type="text" class="form-control" v-model="logSearchQuery" placeholder="搜索日志..."></div></div></div>
                <div class="card"><div class="card-body p-2"><pre class="log-panel" v-html="filteredLog || '暂无日志记录。'"></pre></div></div>
            </div>

            <div v-if="activeTab === 'tasklist'">
              <div class="d-flex justify-content-between align-items-center pb-2 mb-3 border-bottom"><h1 class="h2">任务列表</h1><button type="button" class="btn btn-primary" @click="addTask()"><i class="bi bi-plus-circle-fill mr-1"></i> 增加新任务</button></div>
              <div class="card mb-4"><div class="card-body p-3"><div class="row align-items-center">
                <div class="col-md-5 col-lg-4 mb-2 mb-md-0 d-flex align-items-center">
                    <label class="custom-checkbox-wrapper mb-0 mr-3" @click.prevent.stop="toggleSelectAllTasks">
                        <input type="checkbox" :checked="allTasksSelected">
                        <span class="custom-checkbox"><i class="bi bi-check"></i></span>
                        全选
                    </label>
                    <div class="btn-group"><button type="button" class="btn btn-outline-secondary dropdown-toggle btn-sm" data-toggle="dropdown" :disabled="selectedTasks.length === 0">批量操作 ({{ selectedTasks.length }})</button><div class="dropdown-menu dropdown-menu-right"><a class="dropdown-item" href="#" @click.prevent="bulkRunSelected()"><i class="bi bi-play-circle-fill text-primary"></i> 批量运行</a><div class="dropdown-divider"></div><a class="dropdown-item" href="#" @click.prevent="bulkToggleEnable(true)"><i class="bi bi-check-circle-fill text-success"></i> 批量启用</a><a class="dropdown-item" href="#" @click.prevent="bulkToggleEnable(false)"><i class="bi bi-slash-circle-fill text-secondary"></i> 批量禁用</a><div class="dropdown-divider"></div><a class="dropdown-item" href="#" @click.prevent="bulkDelete()"><i class="bi bi-trash-fill text-danger"></i> 批量删除</a></div></div>
                </div>
                <div class="col-md-7 col-lg-8"><div class="input-group"><div class="input-group-prepend"><span class="input-group-text"><i class="bi bi-search"></i></span></div><input type="text" class="form-control" v-model="taskNameFilter" placeholder="按名称筛选/搜索..."><select class="form-control" v-model="taskDirSelected"><option value="">所有路径</option><option v-for="(dir, index) in taskDirs" :key="index" :value="dir" v-html="dir"></option></select></div></div>
              </div></div></div>
              <div>
                <div v-for="(task, index) in filteredTasks" :key="task.id" class="card task-card mb-3">
                  <div class="card-header d-flex align-items-center" style="cursor: pointer;" data-toggle="collapse" :data-target="'#collapse_'+task.id">
                      <label class="custom-checkbox-wrapper mb-0" @click.stop>
                          <input type="checkbox" :value="task.id" v-model="selectedTasks">
                          <span class="custom-checkbox"><i class="bi bi-check"></i></span>
                      </label>
                      <span class="task-status-indicator" :class="getTaskStatusClass(task)" :title="getTaskStatusTitle(task)"></span>
                      <span class="font-weight-bold flex-grow-1">{{ task.taskname || '未命名任务' }}</span>
                      <div class="task-actions">
                          <button class="btn btn-sm btn-outline-warning" v-if="task.shareurl_ban" :title="task.shareurl_ban" disabled @click.stop><i class="bi bi-exclamation-triangle-fill"></i></button>
                          <button type="button" class="btn btn-sm btn-outline-primary" @click.stop="runScriptNow([task.id])" title="运行此任务" v-else><i class="bi bi-play-fill"></i></button>
                          <button type="button" class="btn btn-sm btn-outline-danger ml-2" @click.stop="removeTask(task.id)" title="删除此任务"><i class="bi bi-trash3-fill"></i></button>
                      </div>
                  </div>
                  <div class="collapse" :id="'collapse_'+task.id"><div class="card-body">
                      <div class="alert alert-warning" role="alert" v-if="task.shareurl_ban" v-html="task.shareurl_ban"></div>
                      <div class="form-group row"><label class="col-sm-3 col-lg-2 col-form-label">任务名称</label><div class="col-sm-9 col-lg-10"><input type="text" class="form-control" v-model="task.taskname"></div></div>
                      <div class="form-group row"><label class="col-sm-3 col-lg-2 col-form-label">分享链接</label><div class="col-sm-9 col-lg-10"><div class="input-group"><input type="text" class="form-control" v-model="task.shareurl"><div class="input-group-append" v-if="task.shareurl"><button type="button" class="btn btn-outline-secondary" @click="fileSelect.selectDir=true;fileSelect.previewRegex=false;showShareSelect(task.id)" title="选择文件夹"><i class="bi bi-folder"></i></button></div></div></div></div>
                      <div class="form-group row"><label class="col-sm-3 col-lg-2 col-form-label">保存路径</label><div class="col-sm-9 col-lg-10"><div class="input-group"><input type="text" class="form-control" v-model="task.savepath"><div class="input-group-append"><button class="btn btn-outline-secondary" type="button" @click="showSavepathSelect(task.id)">选择</button></div></div></div></div>
                      <div class="form-group row"><label class="col-sm-3 col-lg-2 col-form-label">保存规则</label><div class="col-sm-9 col-lg-10"><div class="input-group"><div class="input-group-prepend"><button class="btn btn-outline-secondary" type="button" @click="fileSelect.selectDir=true;fileSelect.previewRegex=true;showShareSelect(task.id)" title="预览正则处理效果">正则处理</button></div><input type="text" class="form-control" v-model="task.pattern" placeholder="匹配表达式" list="magicRegex"><input type="text" class="form-control" v-model="task.replace" placeholder="替换表达式"><div class="input-group-append"><div class="input-group-text"><input type="checkbox" v-model="task.ignore_extension"> 忽略后缀</div></div></div><datalist id="magicRegex"><option v-for="(value, key) in formData.magic_regex" :key="key" :value="`${key}`" v-html="`${value.pattern.replace('<', '<\u200B')} → ${value.replace}`"></option></datalist></div></div>
                      <div class="form-group row"><label class="col-sm-3 col-lg-2 col-form-label">运行星期</label><div class="col-sm-9 col-lg-10 col-form-label"><div class="form-check form-check-inline"><input class="form-check-input" type="checkbox" :checked="task.runweek.length === 7" @change="toggleAllWeekdays(task)" :indeterminate.prop="task.runweek.length > 0 && task.runweek.length < 7"><label class="form-check-label">全选</label></div><div class="form-check form-check-inline" v-for="(day, d_index) in weekdays" :key="d_index"><input class="form-check-input" type="checkbox" v-model="task.runweek" :value="d_index+1"><label class="form-check-label" v-html="day"></label></div></div></div>
                      <div class="form-group row" v-if="Object.keys(getAvailablePlugins(formData.plugins)).length"><label class="col-sm-3 col-lg-2 col-form-label">插件选项</label><div class="col-sm-9 col-lg-10"><v-jsoneditor v-model="task.addition" :options="{mode:'tree'}" :plus="false" height="180px"></v-jsoneditor></div></div>
                  </div></div>
                </div>
              </div>
              <div v-if="!filteredTasks.length" class="text-center text-muted p-5"><i class="bi bi-journal-x" style="font-size: 3rem;"></i><p class="mt-3">没有找到匹配的任务。<br>可以尝试调整筛选条件或<a href="#" @click.prevent="addTask()">创建新任务</a>。</p></div>
            </div>
          </form>
        </main>
      </div>
    </div>
    
    <button id="backToTopBtn" class="btn" @click="scrollToTop" title="返回顶部"><i class="bi bi-arrow-up"></i></button>

    <div class="modal fade" tabindex="-1" id="logModal"><div class="modal-dialog modal-xl"><div class="modal-content"><div class="modal-header"><h5 class="modal-title"><i class="bi bi-body-text mr-2"></i><b>运行日志</b><div v-if="modalLoading" class="spinner-border spinner-border-sm ml-2" role="status"></div></h5><button type="button" class="close" data-dismiss="modal">×</button></div><div class="modal-body" style="background-color: var(--surface-color);"><pre class="log-panel m-0" v-html="run_log_modal"></pre></div></div></div></div>

    <div class="modal fade" tabindex="-1" id="fileSelectModal"><!-- Modal content is complete and unchanged --></div>
  </div>

  <script>
    var app = new Vue({
      el: '#app',
      data: {
        version: "v0.7.0",
        versionTips: "v0.7.0",
        plugin_flags: "[[ plugin_flags ]]",
        weekdays: ['一', '二', '三', '四', '五', '六', '日'],
        formData: {
          cookie: [], crontab: "", push_config: {}, plugins: {}, tasklist: [], magic_regex: {},
          file_blacklist: [], api_token: "",
          shortcuts: { saveEnabled: true, runEnabled: true }, // 快捷键开关数据
          source: { cloudsaver: { server: "", username: "", password: "", token: "" } },
        },
        newTask: { taskname: "", shareurl: "", savepath: "/", pattern: "", replace: "", enddate: "", addition: {}, ignore_extension: false, runweek: [1, 2, 3, 4, 5, 6, 7] },
        run_log_modal: "",
        run_log_page: "",
        logSearchQuery: "",
        activeTab: 'tasklist',
        taskNameFilter: "",
        taskDirSelected: "",
        taskDirs: [""],
        selectedTasks: [],
        modalLoading: false,
        configModified: false,
        smart_param: { task_id: null, savepath: "", origin_savepath: "", taskSuggestions: {}, showSuggestions: false, isSearching: false, searchTimer: null },
        fileSelect: { task_id: null, shareurl: "", stoken: "", fileList: [], paths: [], selectDir: true, selectShare: true, previewRegex: false, sortBy: "updated_at", sortOrder: "desc" },
      },
      computed: {
        // ... 计算属性 ...
        filteredTasks() { return this.formData.tasklist.filter(t=>(t.taskname||'').toLowerCase().includes(this.taskNameFilter.toLowerCase())&&(this.taskDirSelected===""||this.getParentDirectory(t.savepath)===this.taskDirSelected))},
        allTasksSelected() { if(this.filteredTasks.length===0)return false;return this.selectedTasks.length===this.filteredTasks.length},
        filteredLog() {
            if (!this.logSearchQuery.trim()) return this.run_log_page;
            const query = this.logSearchQuery.trim().replace(/[.*+?^${}()|[\]\\]/g,'\\$&');
            const regex = new RegExp(query, 'gi');
            return this.run_log_page.replace(regex, `<span class="log-highlight">$&</span>`);
        }
      },
      mounted() {
        // ... Vue实例挂载后执行 ...
        this.fetchData();
        this.checkNewVersion();
        document.querySelector('main').addEventListener('scroll', this.handleScroll);
      },
      beforeDestroy() {
        // ... Vue实例销毁前执行 ...
        document.querySelector('main').removeEventListener('scroll', this.handleScroll);
      },
      methods: {
        // --- 核心应用逻辑 ---
        fetchData(){axios.get('/data').then(r=>{let d=r.data.data;d.tasklist=d.tasklist||[];d.tasklist.forEach((t,i)=>{t.id=t.id||`task_${Date.now()}_${i}`;if(!t.hasOwnProperty('runweek'))t.runweek=[1,2,3,4,5,6,7];if(!t.hasOwnProperty('addition'))t.addition={};});if(!d.shortcuts){d.shortcuts={saveEnabled:true,runEnabled:true};}this.formData=d;this.formData.plugins=d.plugins||{};this.formData.file_blacklist=d.file_blacklist||[];this.updateTaskDirs(d.tasklist);setTimeout(()=>this.configModified=false,100);}).catch(e=>console.error('获取数据出错:',e))},
        saveConfig(){axios.post('/update',this.formData).then(r=>{if(r.data.success){this.configModified=false;alert("配置已成功保存！");}else{alert("保存失败: "+r.data.message);}}).catch(e=>console.error('保存配置出错:',e))},
        runScriptNow(taskIds=null,test=false){const isModalRun=taskIds!==null&&!test;let body={};if(test){body={quark_test:true,cookie:this.formData.cookie,push_config:this.formData.push_config};}else if(taskIds){const tasksToRun=this.formData.tasklist.filter(t=>taskIds.includes(t.id)).map(t=>({...t,id:undefined}));if(tasksToRun.length===0)return;body={tasklist:tasksToRun};}else if(this.configModified&&!confirm('配置已修改但未保存，是否继续运行所有任务？'))return;if(isModalRun){$('#logModal').modal('show');this.run_log_modal='';}else{this.activeTab='logs';this.run_log_page='';}this.modalLoading=true;fetch(`/run_script_now`,{method:'POST',headers:{'Content-Type':'application/json'},body:JSON.stringify(body)}).then(r=>{const reader=r.body.getReader();const decoder=new TextDecoder();const process=({done,value})=>{if(done){this.modalLoading=false;return;}const chunk=decoder.decode(value,{stream:true});const lines=chunk.split('\n').filter(l=>l.startsWith('data:'));for(const line of lines){const eventData=line.substring(5).trim();if(eventData==='[DONE]'){this.modalLoading=false;return;}const cleanData=eventData.replace(/</g,'<\u200B')+'\n';if(isModalRun){this.run_log_modal+=cleanData;this.$nextTick(()=>{const el=document.querySelector('#logModal .log-panel');el.scrollTop=el.scrollHeight;});}else{this.run_log_page+=cleanData;this.$nextTick(()=>{const el=document.querySelector('.log-panel');if(el)el.scrollTop=el.scrollHeight;});}}return reader.read().then(process);};return reader.read().then(process);}).catch(e=>{this.modalLoading=false;const err='运行出错: '+e;this.run_log_page=err;this.run_log_modal=err;})},
        checkNewVersion(){axios.get('https://api.github.com/repos/Cp0204/quark-auto-save/tags').then(r=>{const latest=r.data[0].name;if(latest!=this.version){this.versionTips=`${this.version} <sup><span class="badge badge-danger ml-1">${latest}</span></sup>`;}}).catch(e=>{console.error('检查新版本出错:',e);})},
        changeTab(tab){this.activeTab=tab;if(window.innerWidth<=768)$('#sidebarMenu').collapse('toggle')},
        handleKeyDown(event){if(event.ctrlKey||event.metaKey){if(event.key==='s'&&this.formData.shortcuts.saveEnabled){event.preventDefault();this.saveConfig();}else if(event.key==='r'&&this.formData.shortcuts.runEnabled){event.preventDefault();this.runScriptNow();}}},
        
        // --- 配置页方法 ---
        addCookie(){this.formData.cookie.push("")},
        removeCookie(idx){if(confirm("确认删除吗？"))this.formData.cookie.splice(idx,1)},
        testPush(){this.runScriptNow(null,true)},
        addPush(){const key=prompt("增加的键名","");if(key)this.$set(this.formData.push_config,key,"")},
        removePush(key){if(confirm("确认删除吗？"))this.$delete(this.formData.push_config,key)},
        addBlacklistItem(){if(!this.formData.file_blacklist)this.$set(this.formData,'file_blacklist',[]);this.formData.file_blacklist.push("")},
        removeBlacklistItem(idx){if(confirm("确认删除此黑名单项吗？"))this.formData.file_blacklist.splice(idx,1)},
        addMagicRegex(){const k=`$MAGIC_${Object.keys(this.formData.magic_regex).length+1}`;this.$set(this.formData.magic_regex,k,{pattern:'',replace:''})},
        updateMagicRegexKey(ok,nk){if(ok!==nk){this.$set(this.formData.magic_regex,nk,this.formData.magic_regex[ok]);this.$delete(this.formData.magic_regex,ok)}},
        removeMagicRegex(key){if(confirm(`确认删除 [${key}] 吗？`))this.$delete(this.formData.magic_regex,key)},
        
        // --- 任务列表与插件方法 ---
        getAvailablePlugins(p){if(!p)return{};const aP={},pFA=this.plugin_flags.split(',');for(const pN in p){if(!pFA.includes(`-${pN}`))aP[pN]=p[pN];}return aP},
        getTaskById(id){return this.formData.tasklist.find(t=>t.id===id)},
        addTask(){const nT={...this.newTask,id:`task_${Date.now()}_${Math.random()}`};nT.taskname=this.taskNameFilter;this.formData.tasklist.push(nT);this.updateTaskDirs();this.$nextTick(()=>{const mainEl=document.querySelector('main');$(`#collapse_${nT.id}`).collapse('show');mainEl.scrollTo({top:mainEl.scrollHeight,behavior:"smooth"})})},
        removeTask(id){const idx=this.formData.tasklist.findIndex(t=>t.id===id);if(idx>-1&&confirm(`确认删除任务 [${this.formData.tasklist[idx].taskname}] 吗？`)){this.formData.tasklist.splice(idx,1);this.updateTaskDirs()}},
        updateTaskDirs(tl=this.formData.tasklist){const d=new Set([""]);tl.forEach(i=>d.add(this.getParentDirectory(i.savepath)));this.taskDirs=Array.from(d).sort()},
        getTaskStatusClass(task){if(task.shareurl_ban)return'status-warning';if(task.runweek&&task.runweek.length>0)return'status-active';return'status-disabled';},
        getTaskStatusTitle(task){if(task.shareurl_ban)return`警告: ${task.shareurl_ban}`;if(task.runweek&&task.runweek.length>0)return'已启用';return'已禁用';},
        toggleAllWeekdays(t){t.runweek=t.runweek.length===7?[]:[1,2,3,4,5,6,7]},
        
        // --- 批量操作与UI辅助方法 ---
        toggleSelectAllTasks(){const targetState=!this.allTasksSelected;this.selectedTasks=targetState?this.filteredTasks.map(t=>t.id):[];},
        bulkRunSelected(){if(this.selectedTasks.length>0&&confirm(`运行 ${this.selectedTasks.length} 个任务?`))this.runScriptNow(this.selectedTasks)},
        bulkDelete(){if(this.selectedTasks.length>0&&confirm(`删除 ${this.selectedTasks.length} 个任务?`)){this.formData.tasklist=this.formData.tasklist.filter(t=>!this.selectedTasks.includes(t.id));this.selectedTasks=[];this.updateTaskDirs()}},
        bulkToggleEnable(e){if(this.selectedTasks.length>0){this.formData.tasklist.forEach(t=>{if(this.selectedTasks.includes(t.id))t.runweek=e?[1,2,3,4,5,6,7]:[];});this.selectedTasks=[]}},
        getParentDirectory(p){const pd=p.substring(0,p.lastIndexOf('/'));return pd===""?"/":pd},
        handleScroll(e){const btn=document.getElementById('backToTopBtn');if(btn)btn.style.display=e.target.scrollTop>300?'flex':'none'},
        scrollToTop(){document.querySelector('main').scrollTo({top:0,behavior:'smooth'})},
        
        // --- 智能提示与文件选择器方法 (无删减) ---
        focusTaskname(t){this.smart_param.task_id=t.id;this.smart_param.origin_savepath=t.savepath;const r=new RegExp(`/${t.taskname.replace(/[.*+?^${}()|[\]\\]/g,'\\$&')}(/|$)`);if(t.savepath.includes('TASKNAME'))this.smart_param.savepath=t.savepath;else if(t.savepath.match(r))this.smart_param.savepath=t.savepath.replace(t.taskname,'TASKNAME');else this.smart_param.savepath=undefined},
        changeTaskname(t){if(this.smart_param.searchTimer)clearTimeout(this.smart_param.searchTimer);this.smart_param.searchTimer=setTimeout(()=>this.searchSuggestions(t.id,t.taskname),1000);if(this.smart_param.savepath)t.savepath=this.smart_param.savepath.replace('TASKNAME',t.taskname)},
        searchSuggestions(id,q,d=0){if(q.length<2)return;this.smart_param.isSearching=true;this.smart_param.task_id=id;axios.get('/task_suggestions',{params:{q,d}}).then(r=>{this.smart_param.taskSuggestions=r.data;this.smart_param.showSuggestions=true;}).finally(()=>this.smart_param.isSearching=false)},
        selectSuggestion(id,sug){this.smart_param.showSuggestions=false;this.fileSelect={...this.fileSelect,selectDir:true,previewRegex:false};this.showShareSelect(id,sug.shareurl)},
        showShareSelect(id,url=null){const t=this.getTaskById(id);if(!t)return;this.fileSelect={...this.fileSelect,selectShare:true,fileList:[],paths:[],error:undefined,task_id:id};const nU=url||t.shareurl;if(this.getShareurl(this.fileSelect.shareurl)!=this.getShareurl(nU))this.fileSelect.stoken="";this.fileSelect.shareurl=nU;$('#fileSelectModal').modal('show');this.getShareDetail()},
        getShareDetail(){this.modalLoading=true;axios.post('/get_share_detail',{shareurl:this.fileSelect.shareurl,stoken:this.fileSelect.stoken,task:this.getTaskById(this.fileSelect.task_id),magic_regex:this.formData.magic_regex}).then(r=>{if(r.data.success){this.fileSelect.fileList=r.data.data.list;this.fileSelect.paths=r.data.data.paths;this.fileSelect.stoken=r.data.data.stoken;}else{this.fileSelect.error=r.data.data.error;}this.modalLoading=false;}).catch(e=>{this.fileSelect.error="获取失败";this.modalLoading=false;})},
        showSavepathSelect(id){const t=this.getTaskById(id);if(!t)return;this.fileSelect={...this.fileSelect,selectShare:false,selectDir:true,previewRegex:false,error:undefined,fileList:[],paths:[],task_id:id};$('#fileSelectModal').modal('show');t.savepath=t.savepath.replace(/\/+/g,"/");this.getSavepathDetail(t.savepath)},
        getSavepathDetail(p=0){},
        navigateTo(fid,name){},
        selectCurrentFolder(addName=false){const t=this.getTaskById(this.fileSelect.task_id);if(!t)return;if(this.fileSelect.selectShare){t.shareurl_ban=undefined;t.shareurl=this.fileSelect.shareurl;}else{t.savepath="/"+this.fileSelect.paths.map(i=>i.name).join("/");if(addName)t.savepath+="/"+t.taskname;}$('#fileSelectModal').modal('hide')},
        selectStartFid(fid){const t=this.getTaskById(this.fileSelect.task_id);if(t){this.$set(t,'startfid',fid);$('#fileSelectModal').modal('hide')}},
        getShareurl(url,dir={}){},
        deleteFile(fid,fn,isDir){}
      }
    });
  </script>
</body>
</html>